from abc import ABC, abstractmethod
from base import NamedItem, Point, Dimension, Box, Cylinder, Side, Plane
from dowel import LocatedDowel
# from physical_item import LocatedPhysicalItem
from enum import Enum
from dataclasses import dataclass, field


@dataclass
class Hole(ABC):
    """Represents a hole in a board."""
    diameter: int
    depth: int


@dataclass
class FaceHole(Hole, ABC):
    position_from_left: int
    position_from_bottom: int

    def __post_init__(self):
        if self.diameter not in [3, 5, 8, 10, 15, 20, 35]:
            raise ValueError("Invalid face hole diameter " + str(self.diameter) +
                             "mm, allowed values: 3, 5, 8, 10, 15, 20, 35mm")
        if self.depth not in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, -1]:
            raise ValueError("Invalid face hole depth " + str(self.depth) +
                             "mm, allowed values: 2-15mm and -1 as through hole")


@dataclass
class BackHole(FaceHole):
    pass


@dataclass
class FrontHole(FaceHole):
    pass


@dataclass
class SideHole(Hole, ABC):
    def __post_init__(self):
        if self.diameter not in [4, 8]:
            raise ValueError("Invalid side hole diameter " + str(self.diameter) + "mm, allowed values: 4, 8mm")
        if self.depth < 2 or self.depth > 35:
            raise ValueError("Invalid side hole depth " + str(self.depth) + "mm, allowed values: 2-35mm")


@dataclass
class LeftRightHole(SideHole, ABC):
    position_from_bottom: int


@dataclass
class LeftHole(LeftRightHole):
    pass


@dataclass
class RightHole(LeftRightHole):
    pass


@dataclass
class TopBottomHole(SideHole, ABC):
    position_from_left: int


@dataclass
class TopHole(TopBottomHole):
    pass


@dataclass
class BottomHole(TopBottomHole):
    pass


class GrooveDepth(int):
    def __new__(cls, value):
        if value not in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]:
            raise ValueError("Invalid groove depth " + str(value) + "mm, allowed values: 2-13mm")
        return super().__new__(cls, value)


class GrooveWidth(float):
    def __new__(cls, value):
        if value not in [3.2, 4.2, 10.2]:
            raise ValueError("Invalid groove width " + str(value) + "mm, allowed values: 3.2, 4.2, 10.2mm")
        return super().__new__(cls, value)


class GrooveDistanceFromEdge(int):
    def __new__(cls, value):
        if not 0 <= value <= 50:
            raise ValueError("Groove distance must be in distance 0-50.")
        return super().__new__(cls, value)


@dataclass
class Groove():
    face: Side
    edge: Side
    depth: GrooveDepth
    width: GrooveWidth
    distance_from_edge: GrooveDistanceFromEdge
    distance_1_if_not_through: int
    distance_2_if_not_through: int


class BandingType(Enum):
    """Represents the type of banding."""
    NONE = "none"
    THIN = "0.8mm"
    THICK = "2mm"
    REGULAR = "regular face"
    SPECIAL = "special face"


@dataclass
class Board(NamedItem):
    """Represents a board in the cabinet."""
    name: str
    width: int
    height: int
    thickness: int
    holes: list[Hole] = field(default_factory=list)
    grooves: list[Groove] = field(default_factory=list)
    banding_top: BandingType = BandingType.NONE
    banding_bottom: BandingType = BandingType.NONE
    banding_left: BandingType = BandingType.NONE
    banding_right: BandingType = BandingType.NONE

    def get_name(self) -> str:
        return self.name


@dataclass
class LocatedBoard(ABC):
    board: Board
    location: Point

    @abstractmethod
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        pass

    def get_location_in_dimension(self, dimension: Dimension) -> float:
        if dimension == Dimension.X:
            return self.location.x
        elif dimension == Dimension.Y:
            return self.location.y
        elif dimension == Dimension.Z:
            return self.location.z
        else:
            raise ValueError("Invalid dimension")

    @abstractmethod
    def get_shape(self) -> Box:
        pass

    @abstractmethod
    def get_groove_boxes(self) -> list[Box]:
        pass

    def colides_with_board(self, other: 'LocatedBoard') -> bool:
        main_box = self.get_shape()
        other_main_box = other.get_shape()
        main_boxes_intersection = main_box.intersection_with_box(other_main_box)
        if main_boxes_intersection.is_empty():
            return False
        for groove_box in self.get_groove_boxes() + other.get_groove_boxes():
            if groove_box.fully_contains(main_boxes_intersection):
                return False
        return not main_boxes_intersection.is_empty()

    def colides_with_dowel(self, dowel: LocatedDowel) -> bool:
        main_box = self.get_shape()
        dowel_cylinder = dowel.get_cylinder()
        intersection = dowel_cylinder.intersection_with_box(main_box)
        if intersection.is_empty():
            return False
        for hole_cylinder in self.get_hole_cylinders():
            if hole_cylinder.fully_contains(intersection):
                return False
        return True

    @abstractmethod
    def get_hole_cylinders(self) -> list[Cylinder]:
        pass

    @abstractmethod
    def get_side(self, side: Side) -> tuple[Plane, BandingType]:
        pass


class FlatLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.width
        elif dimension == Dimension.Y:
            return self.board.height
        elif dimension == Dimension.Z:
            return self.board.thickness
        else:
            raise ValueError("Invalid dimension")

    def get_shape(self) -> Box:
        return Box(0, self.board.width, 0, self.board.height, 0, self.board.thickness) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.height - groove.distance_from_edge - groove.width
                    y2 = self.board.height - groove.distance_from_edge
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = self.board.thickness - groove.depth
                    z2 = self.board.thickness
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.height - groove.distance_from_edge - groove.width
                    y2 = self.board.height - groove.distance_from_edge
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = 0
                    z2 = groove.depth
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = max(groove.distance_2_if_not_through, 0)
                    y2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                    z1 = 0
                    z2 = groove.depth
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append(Box(x1, x2, y1, y2, z1, z2)+self.location)
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = hole.position_from_left
                y = hole.position_from_bottom
                z = self.board.thickness - depth
                dimension = Dimension.Z
            elif isinstance(hole, BackHole):
                x = hole.position_from_left
                y = hole.position_from_bottom
                z = 0
                dimension = Dimension.Z
            elif isinstance(hole, LeftHole):
                x = 0
                y = hole.position_from_bottom
                z = self.board.thickness/2
                dimension = Dimension.X
            elif isinstance(hole, RightHole):
                x = self.board.width - depth
                y = hole.position_from_bottom
                z = self.board.thickness/2
                dimension = Dimension.X
            elif isinstance(hole, TopHole):
                x = hole.position_from_left
                y = self.board.height - depth
                z = self.board.thickness/2
                dimension = Dimension.Y
            elif isinstance(hole, BottomHole):
                x = hole.position_from_left
                y = 0
                z = self.board.thickness/2
                dimension = Dimension.Y
            else:
                raise ValueError("Invalid hole type")
            result.append(Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location)
        return result

    def get_side(self, side: Side) -> tuple[Plane, BandingType]:
        main_box = self.get_shape()
        if side == Side.FRONT:
            return (Plane(main_box.z_max, Dimension.Z), BandingType.REGULAR)
        elif side == Side.BACK:
            return (Plane(main_box.z_min, Dimension.Z), BandingType.REGULAR)
        elif side == Side.TOP:
            return (Plane(main_box.y_max, Dimension.Y), self.board.banding_top)
        elif side == Side.BOTTOM:
            return (Plane(main_box.y_min, Dimension.Y), self.board.banding_bottom)
        elif side == Side.LEFT:
            return (Plane(main_box.x_min, Dimension.X), self.board.banding_left)
        elif side == Side.RIGHT:
            return (Plane(main_box.x_max, Dimension.X), self.board.banding_right)
        else:
            raise ValueError("Invalid side")


class SideLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.thickness
        elif dimension == Dimension.Y:
            return self.board.width
        elif dimension == Dimension.Z:
            return self.board.height
        else:
            raise ValueError("Invalid dimension")

    def get_shape(self) -> Box:
        return Box(0, self.board.thickness, 0, self.board.width, 0, self.board.height) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.thickness - groove.depth
                    x2 = self.board.thickness
                    y1 = self.board.width - groove.distance_from_edge - groove.width
                    y2 = self.board.width - groove.distance_from_edge
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = 0
                    x2 = groove.depth
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = 0
                    x2 = groove.depth
                    y1 = max(groove.distance_1_if_not_through, 0)
                    y2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = 0
                    x2 = groove.depth
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = 0
                    x2 = groove.depth
                    y1 = self.board.width - groove.distance_from_edge - groove.width
                    y2 = self.board.width - groove.distance_from_edge
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append(Box(x1, x2, y1, y2, z1, z2)+self.location)
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = self.board.thickness - depth
                y = hole.position_from_left
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, BackHole):
                x = 0
                y = hole.position_from_left
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, LeftHole):
                x = self.board.thickness/2
                y = 0
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, RightHole):
                x = self.board.thickness/2
                y = self.board.width - depth
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, TopHole):
                x = self.board.thickness/2
                y = hole.position_from_left
                z = self.board.height - depth
                dimension = Dimension.Z
            elif isinstance(hole, BottomHole):
                x = self.board.thickness/2
                y = hole.position_from_left
                z = 0
                dimension = Dimension.Z
            else:
                raise ValueError("Invalid hole type")
            result.append(Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location)
        return result

    def get_side(self, side: Side) -> tuple[Plane, BandingType]:
        main_box = self.get_shape()
        if side == Side.FRONT:
            return (Plane(main_box.x_max, Dimension.X), BandingType.REGULAR)
        elif side == Side.BACK:
            return (Plane(main_box.x_min, Dimension.X), BandingType.REGULAR)
        elif side == Side.TOP:
            return (Plane(main_box.z_max, Dimension.Z), self.board.banding_top)
        elif side == Side.BOTTOM:
            return (Plane(main_box.z_min, Dimension.Z), self.board.banding_bottom)
        elif side == Side.LEFT:
            return (Plane(main_box.y_min, Dimension.Y), self.board.banding_left)
        elif side == Side.RIGHT:
            return (Plane(main_box.y_max, Dimension.Y), self.board.banding_right)
        else:
            raise ValueError("Invalid side")


class FrontLocatedBoard(LocatedBoard):
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if dimension == Dimension.X:
            return self.board.width
        elif dimension == Dimension.Y:
            return self.board.thickness
        elif dimension == Dimension.Z:
            return self.board.height
        else:
            raise ValueError("Invalid dimension")

    def get_shape(self) -> Box:
        return Box(0, self.board.width, 0, self.board.thickness, 0, self.board.height) + self.location

    def get_groove_boxes(self) -> list[Box]:
        result: list[Box] = []
        x1: float
        x2: float
        y1: float
        y2: float
        z1: float
        z2: float
        for groove in self.board.grooves:
            if groove.face == Side.FRONT:
                if groove.edge == Side.TOP:
                    x1 = float(max(groove.distance_1_if_not_through, 0))
                    x2 = float(self.board.width - max(groove.distance_2_if_not_through, 0))
                    y1 = 0.0
                    y2 = float(groove.depth)
                    z1 = float(self.board.height - groove.distance_from_edge - groove.width)
                    z2 = float(self.board.height - groove.distance_from_edge)
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = 0
                    y2 = groove.depth
                    z1 = groove.distance_from_edge
                    z2 = groove.width + groove.distance_from_edge
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = 0
                    y2 = groove.depth
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = 0
                    y2 = groove.depth
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            elif groove.face == Side.BACK:
                if groove.edge == Side.TOP:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = self.board.height - groove.distance_from_edge - groove.width
                    z2 = self.board.height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    x1 = max(groove.distance_1_if_not_through, 0)
                    x2 = self.board.width - max(groove.distance_2_if_not_through, 0)
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = groove.distance_from_edge
                    z2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                elif groove.edge == Side.RIGHT:
                    x1 = self.board.width - groove.distance_from_edge - groove.width
                    x2 = self.board.width - groove.distance_from_edge
                    y1 = self.board.thickness - groove.depth
                    y2 = self.board.thickness
                    z1 = max(groove.distance_2_if_not_through, 0)
                    z2 = self.board.height - max(groove.distance_1_if_not_through, 0)
                else:
                    raise ValueError("Invalid groove edge")
            else:
                raise ValueError("Invalid groove face")
            result.append(Box(x1, x2, y1, y2, z1, z2)+self.location)
        return result

    def get_hole_cylinders(self) -> list[Cylinder]:
        result: list[Cylinder] = []
        x: float
        y: float
        z: float
        dimension: Dimension
        for hole in self.board.holes:
            depth = hole.depth
            if depth == -1:
                depth = self.board.thickness
            if isinstance(hole, FrontHole):
                x = float(hole.position_from_left)
                y = 0.0
                z = float(hole.position_from_bottom)
                dimension = Dimension.Y
            elif isinstance(hole, BackHole):
                x = hole.position_from_left
                y = self.board.thickness - depth
                z = hole.position_from_bottom
                dimension = Dimension.Y
            elif isinstance(hole, LeftHole):
                x = 0
                y = self.board.thickness/2
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, RightHole):
                x = self.board.width - depth
                y = self.board.thickness/2
                z = hole.position_from_bottom
                dimension = Dimension.X
            elif isinstance(hole, TopHole):
                x = hole.position_from_left
                y = self.board.thickness/2
                z = self.board.height - depth
                dimension = Dimension.Z
            elif isinstance(hole, BottomHole):
                x = hole.position_from_left
                y = self.board.thickness/2
                z = 0
                dimension = Dimension.Z
            else:
                raise ValueError("Invalid hole type")
            result.append(Cylinder.cylinder_factory(hole.diameter/2, depth, Point(x, y, z), dimension) + self.location)
        return result

    def get_side(self, side: Side) -> tuple[Plane, BandingType]:
        main_box = self.get_shape()
        if side == Side.FRONT:
            return (Plane(main_box.y_min, Dimension.Y), BandingType.REGULAR)
        elif side == Side.BACK:
            return (Plane(main_box.y_max, Dimension.Y), BandingType.REGULAR)
        elif side == Side.TOP:
            return (Plane(main_box.z_max, Dimension.Z), self.board.banding_top)
        elif side == Side.BOTTOM:
            return (Plane(main_box.z_min, Dimension.Z), self.board.banding_bottom)
        elif side == Side.LEFT:
            return (Plane(main_box.x_min, Dimension.X), self.board.banding_left)
        elif side == Side.RIGHT:
            return (Plane(main_box.x_max, Dimension.X), self.board.banding_right)
        else:
            raise ValueError("Invalid side")
