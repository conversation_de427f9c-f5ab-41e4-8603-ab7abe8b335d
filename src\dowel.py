from base import Orientation, Point, Cylinder, XCylinder, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>inder
from dataclasses import dataclass


@dataclass
class Dowel:
    """Represents a dowel in the cabinet."""
    name: str
    length: int
    diameter: int

    def get_name(self) -> str:
        return self.name


@dataclass
class LocatedDowel:
    dowel: Dowel
    orientation: Orientation
    location: Point

    def get_cylinder(self) -> Cylinder:
        if self.orientation == Orientation.FRONT:
            return <PERSON><PERSON><PERSON>inder(self.dowel.diameter / 2, self.dowel.length, self.location)
        elif self.orientation == Orientation.FLAT:
            return Y<PERSON><PERSON><PERSON>(self.dowel.diameter / 2, self.dowel.length, self.location)
        elif self.orientation == Orientation.SIDE:
            return XCylinder(self.dowel.diameter / 2, self.dowel.length, self.location)

    def colides_with_dowel(self, other: 'LocatedDowel') -> bool:
        return self.get_cylinder().overlaps_with_cylinder(other.get_cylinder())
