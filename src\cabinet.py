from dataclasses import dataclass, field
from board import LocatedBoard
from dowel import LocatedDowel


@dataclass
class Cabinet:
    """Represents a cabinet."""
    boards: list[LocatedBoard] = field(default_factory=list)
    dowels: list[LocatedDowel] = field(default_factory=list)

    def add_board(self, board: LocatedBoard) -> None:
        for existing_board in self.boards:
            if board.colides_with_board(existing_board):
                print("Collision: " + board.board.name + " and " + existing_board.board.name)
        for existing_dowel in self.dowels:
            if board.colides_with_dowel(existing_dowel):
                print("Collision: " + board.board.name + " and " + existing_dowel.dowel.name)
        self.boards.append(board)

    def add_dowel(self, dowel: LocatedDowel) -> None:
        for existing_board in self.boards:
            if existing_board.colides_with_dowel(dowel):
                print("Collision: " + dowel.dowel.name + " and " + existing_board.board.name)
        for existing_dowel in self.dowels:
            if existing_dowel.colides_with_dowel(dowel):
                print("Collision x: " + dowel.dowel.name + " and " + existing_dowel.dowel.name)
        self.dowels.append(dowel)

    def get_board(self, name: str) -> LocatedBoard:
        for board in self.boards:
            if board.board.name == name:
                return board
        raise Exception("Board not found: " + name)

    def get_dowel(self, name: str) -> LocatedDowel:
        for dowel in self.dowels:
            if dowel.dowel.name == name:
                return dowel
        raise Exception("Dowel not found: " + name)
