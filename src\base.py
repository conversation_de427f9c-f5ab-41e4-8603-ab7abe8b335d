from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass


class Dimension(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"


class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

    def get_primary_dimension(self) -> Dimension:
        if self == Orientation.FLAT:
            return Dimension.Y
        elif self == Orientation.FRONT:
            return Dimension.Z
        elif self == Orientation.SIDE:
            return Dimension.X
        else:
            raise ValueError("Invalid orientation")


@dataclass
class Point:
    """Represents a point in 3D space."""
    x: float
    y: float
    z: float

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> "Point":
        return Point(rotate_point.x + rotate_point.y - self.y, rotate_point.y +
                     rotate_point.x - self.x - rotate_point.x, self.z)

    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "Point":
        return Point(rotate_point.x + self.x - rotate_point.x, rotate_point.y +
                     rotate_point.y - self.y, self.z)

    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> "Point":
        return Point(rotate_point.x + self.y - rotate_point.y, rotate_point.y +
                     rotate_point.x - self.x, self.z)


@dataclass
class Plane:
    offset: float
    normal: Dimension

    def __eq__(self, value):
        return abs(self.offset - value.offset) < 0.000001 and\
                   self.normal == value.normal


class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass


class Side(Enum):
    """Represents the side of a board."""
    FRONT = "front"
    BACK = "back"
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"


@dataclass
class Box():
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

    def __post_init__(self):
        if self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max:
            self.x_min = 0
            self.x_max = 0
            self.y_min = 0
            self.y_max = 0
            self.z_min = 0
            self.z_max = 0

    def __add__(self, other: "Point") -> "Box":
        return Box(self.x_min + other.x, self.x_max + other.x,
                   self.y_min + other.y, self.y_max + other.y,
                   self.z_min + other.z, self.z_max + other.z)

    def is_empty(self) -> bool:
        return self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max

    def intersection_with_box(self, other: "Box") -> "Box":
        x_min = max(self.x_min, other.x_min)
        x_max = min(self.x_max, other.x_max)
        y_min = max(self.y_min, other.y_min)
        y_max = min(self.y_max, other.y_max)
        z_min = max(self.z_min, other.z_min)
        z_max = min(self.z_max, other.z_max)
        return Box(x_min, x_max, y_min, y_max, z_min, z_max)

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Box):
            return NotImplemented
        return self.x_min == other.x_min and self.x_max == other.x_max and\
            self.y_min == other.y_min and self.y_max == other.y_max and\
            self.z_min == other.z_min and self.z_max == other.z_max

    def __str__(self):
        return f"Box({self.x_min} - {self.x_max}, {self.y_min} - {self.y_max}, {self.z_min} - {self.z_max})"

    def fully_contains(self, other: "Box") -> bool:
        return self.x_min <= other.x_min and self.x_max >= other.x_max and\
            self.y_min <= other.y_min and self.y_max >= other.y_max and\
            self.z_min <= other.z_min and self.z_max >= other.z_max

    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> "Box":
        return Box(rotate_point.x + self.y_min - rotate_point.y,
                   rotate_point.x + self.y_max - rotate_point.y,
                   rotate_point.y + rotate_point.x - self.x_max,
                   rotate_point.y + rotate_point.x - self.x_min,
                   self.z_min, self.z_max)

    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "Box":
        return Box(rotate_point.x + self.x_min - rotate_point.x,
                   rotate_point.x + self.x_max - rotate_point.x,
                   rotate_point.y + self.y_min - rotate_point.y,
                   rotate_point.y + self.y_max - rotate_point.y,
                   self.z_min, self.z_max)

    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> "Box":
        return Box(rotate_point.x + self.y_min - rotate_point.y,
                   rotate_point.x + self.y_max - rotate_point.y,
                   rotate_point.y + rotate_point.x - self.x_min,
                   rotate_point.y + rotate_point.x - self.x_max,
                   self.z_min, self.z_max)


@dataclass
class Cylinder(ABC):
    radius: float
    height: float
    center: Point

    def is_empty(self):
        return self.radius <= 0 or self.height <= 0

    @abstractmethod
    def __add__(self, other: Point) -> "Cylinder":
        pass

    @abstractmethod
    def _get_axis_aligned_bouding_box(self) -> Box:
        pass

    def overlaps_with_cylinder(self, other: "Cylinder") -> bool:
        self_box = self._get_axis_aligned_bouding_box()
        other_box = other._get_axis_aligned_bouding_box()
        intersection = self_box.intersection_with_box(other_box)
        return not intersection.is_empty()

    @abstractmethod
    def intersection_with_box(self, box: Box) -> "Cylinder":
        pass

    @abstractmethod
    def fully_contains(self, other: "Cylinder") -> bool:
        pass

    @abstractmethod
    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> "Cylinder":
        pass

    @abstractmethod
    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "Cylinder":
        pass

    @abstractmethod
    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> "Cylinder":
        pass

    @staticmethod
    def cylinder_factory(radius: float, height: float, center: Point, dimension: Dimension) -> "Cylinder":
        if dimension == Dimension.X:
            return XCylinder(radius, height, center)
        elif dimension == Dimension.Y:
            return YCylinder(radius, height, center)
        elif dimension == Dimension.Z:
            return ZCylinder(radius, height, center)
        else:
            raise ValueError("Invalid dimension")


class XCylinder(Cylinder):

    def intersection_with_box(self, box: Box) -> "XCylinder":
        if self.center.x > box.x_max or self.center.x + self.height < box.x_min:
            return XCylinder(0, 0, self.center)
        if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
            return XCylinder(0, 0, self.center)
        if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
            return XCylinder(0, 0, self.center)
        x_min = max(box.x_min, self.center.x)
        x_max = min(box.x_max, self.center.x + self.height)
        return XCylinder(self.radius, x_max - x_min, Point(x_min, self.center.y, self.center.z))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.y != other.center.y or self.center.z != other.center.z:
            return False
        if self.center.x > other.center.x or self.center.x + self.height < other.center.x + other.height:
            return False
        return True

    def _get_axis_aligned_bouding_box(self) -> Box:
        return Box(self.center.x,
                   self.center.x + self.height,
                   self.center.y - self.radius,
                   self.center.y + self.radius,
                   self.center.z - self.radius,
                   self.center.z + self.radius)

    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> "YCylinder":
        return YCylinder(self.radius, self.height,
                         self.center.rotate_90_clockwise_along_z(rotate_point)+Point(0, -self.height, 0))

    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "XCylinder":
        new_center = self.center.rotate_180_clockwise_along_z(rotate_point) + Point(-self.height, 0, 0)
        return XCylinder(self.radius, self.height, new_center)

    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> "YCylinder":
        return YCylinder(self.radius, self.height, self.center.rotate_270_clockwise_along_z(rotate_point))

    def __add__(self, other: Point) -> "XCylinder":
        return XCylinder(self.radius, self.height, self.center + other)


class YCylinder(Cylinder):
    def intersection_with_box(self, box: Box) -> "YCylinder":
        if self.center.y > box.y_max or self.center.y + self.height < box.y_min:
            return YCylinder(0, 0, self.center)
        if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
            return YCylinder(0, 0, self.center)
        if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
            return YCylinder(0, 0, self.center)
        y_min = max(box.y_min, self.center.y)
        y_max = min(box.y_max, self.center.y + self.height)
        return YCylinder(self.radius, y_max - y_min, Point(self.center.x, y_min, self.center.z))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.x != other.center.x or self.center.z != other.center.z:
            return False
        if self.center.y > other.center.y or self.center.y + self.height < other.center.y + other.height:
            return False
        return True

    def _get_axis_aligned_bouding_box(self) -> Box:
        return Box(self.center.x - self.radius,
                   self.center.x + self.radius,
                   self.center.y, self.center.y + self.height,
                   self.center.z - self.radius,
                   self.center.z + self.radius)

    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> XCylinder:
        return XCylinder(self.radius, self.height,
                         self.center.rotate_90_clockwise_along_z(rotate_point))

    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "YCylinder":
        return YCylinder(self.radius, self.height,
                         self.center.rotate_180_clockwise_along_z(rotate_point)+Point(0, -self.height, 0))

    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> XCylinder:
        return XCylinder(self.radius, self.height,
                         self.center.rotate_270_clockwise_along_z(rotate_point)+Point(-self.height, 0, 0))

    def __add__(self, other: Point) -> "YCylinder":
        return YCylinder(self.radius, self.height, self.center + other)


class ZCylinder(Cylinder):
    def intersection_with_box(self, box: Box) -> "ZCylinder":
        if self.center.z > box.z_max or self.center.z + self.height < box.z_min:
            return ZCylinder(0, 0, self.center)
        if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
            return ZCylinder(0, 0, self.center)
        if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
            return ZCylinder(0, 0, self.center)
        z_min = max(box.z_min, self.center.z)
        z_max = min(box.z_max, self.center.z + self.height)
        return ZCylinder(self.radius, z_max - z_min, Point(self.center.x, self.center.y, z_min))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.x != other.center.x or self.center.y != other.center.y:
            return False
        if self.center.z > other.center.z or self.center.z + self.height < other.center.z + other.height:
            return False
        return True

    def _get_axis_aligned_bouding_box(self) -> Box:
        return Box(self.center.x - self.radius,
                   self.center.x + self.radius,
                   self.center.y - self.radius,
                   self.center.y + self.radius,
                   self.center.z,
                   self.center.z + self.height)

    def rotate_90_clockwise_along_z(self, rotate_point: "Point") -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_90_clockwise_along_z(rotate_point))

    def rotate_180_clockwise_along_z(self, rotate_point: "Point") -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_180_clockwise_along_z(rotate_point))

    def rotate_270_clockwise_along_z(self, rotate_point: "Point") -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_270_clockwise_along_z(rotate_point))

    def __add__(self, other: Point) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center + other)
