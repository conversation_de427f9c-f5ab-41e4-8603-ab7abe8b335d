{"data_mtime": 1759087464, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["dataclasses", "board", "dowel", "builtins", "_frozen_importlib", "_typeshed", "abc", "base", "enum", "typing"], "hash": "6460739a041ed35b4f04749232a5e9db1665038d", "id": "cabinet", "ignore_all": false, "interface_hash": "6bc3d934b2c06628a10f7ec36c0c6e8e46bf2211", "mtime": 1759087464, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\cabinet.py", "plugin_data": null, "size": 1699, "suppressed": [], "version_id": "1.15.0"}